# 🚀 Real-time Collaboration Quick Start

Get your React Flow collaboration system up and running in 5 minutes!

## Prerequisites

- Node.js 18+
- Docker (for Redis)
- PostgreSQL database (Supabase recommended)

## 1. Install Dependencies

```bash
npm install
```

## 2. Start Redis

```bash
# Using Docker (recommended)
docker run -d --name redis-collaboration -p 6379:6379 redis:alpine

# Verify Redis is running
docker ps | grep redis
```

## 3. Configure Environment

Copy `.env.example` to `.env` and update:

```env
# Database (use your Supabase URL)
DATABASE_URL="postgresql://postgres:<EMAIL>:5432/postgres"
DIRECT_URL="postgresql://postgres:<EMAIL>:5432/postgres"

# Redis (local)
REDIS_URL="redis://localhost:6379"

# WebSocket
NEXT_PUBLIC_WS_URL="http://localhost:3000"

# Auth (generate a random secret)
NEXTAUTH_SECRET="your-super-secret-key-here"
NEXTAUTH_URL="http://localhost:3000"
```

## 4. Setup Database

```bash
# Push schema to database
npm run db:push

# Optional: Open Prisma Studio to view data
npm run db:studio
```

## 5. Test the System

```bash
# Run automated tests
npm run test:collaboration

# Should show all green checkmarks ✅
```

## 6. Start Development Server

```bash
npm run dev
```

## 7. Try It Out!

1. **Open your browser** to `http://localhost:3000`

2. **Sign in** (create an account if needed)

3. **Navigate to Flow Rooms**: Click "Flow Rooms" in the sidebar or go to `/flow`

4. **Create a room**:
   - Click "Create Room"
   - Enter a name like "My First Collaboration"
   - Click "Create Room"

5. **Test collaboration**:
   - Open the room in multiple browser tabs
   - Drag nodes around - see them move in real-time!
   - Watch cursor movements from other "users"

## 🎯 What You Should See

### Connection Status
- Green "Connected" indicator in top-left
- Collaboration panel on the right showing participants

### Real-time Features
- **Node dragging**: Smooth real-time updates
- **Cursor tracking**: See other users' mouse positions
- **Instant sync**: Changes appear immediately in other tabs

### Performance
- **Debounced updates**: No lag during rapid movements
- **Automatic reconnection**: Works even with network interruptions

## 🔧 Troubleshooting

### Redis Issues
```bash
# Check if Redis is running
docker ps | grep redis

# View Redis logs
docker logs redis-collaboration

# Test Redis connection
redis-cli ping
# Should return: PONG
```

### Database Issues
```bash
# Reset database if needed
npm run db:push --force-reset

# Check connection in Prisma Studio
npm run db:studio
```

### WebSocket Issues
- Check browser console for errors
- Verify `NEXT_PUBLIC_WS_URL` matches your dev server
- Try refreshing the page

## 🧪 Testing Different Scenarios

### Multi-User Simulation
1. Open room in Chrome tab
2. Open same room in Firefox
3. Open same room in incognito mode
4. Try editing simultaneously

### Network Interruption
1. Open room in two tabs
2. Disconnect internet briefly
3. Reconnect - should auto-sync

### Permission Testing
1. Create room as User A
2. Add User B as "Viewer"
3. User B should see "Read-only mode"

## 📱 Mobile Testing

The collaboration works on mobile too:
1. Open room on desktop
2. Open same room on mobile browser
3. Touch and drag nodes on mobile
4. See updates on desktop instantly

## 🎉 Success Indicators

You'll know it's working when:
- ✅ All tests pass (`npm run test:collaboration`)
- ✅ Multiple tabs sync in real-time
- ✅ Cursor movements are visible
- ✅ Connection status shows "Connected"
- ✅ No errors in browser console

## 🚀 Next Steps

Now that collaboration is working:

1. **Customize nodes**: Edit `src/components/reactflow/custom-nodes.tsx`
2. **Add features**: Implement text chat, voice calls, etc.
3. **Scale up**: Deploy to production with Redis cluster
4. **Monitor**: Add analytics and performance tracking

## 📚 Learn More

- [Full Documentation](./COLLABORATION.md)
- [Architecture Overview](./COLLABORATION.md#architecture)
- [Production Deployment](./COLLABORATION.md#production-deployment)
- [Security Guide](./COLLABORATION.md#security)

## 🆘 Need Help?

If you're stuck:
1. Check the [Troubleshooting Guide](./COLLABORATION.md#troubleshooting)
2. Run the test page: `/flow/test`
3. Review browser console errors
4. Verify all environment variables are set

Happy collaborating! 🎊
