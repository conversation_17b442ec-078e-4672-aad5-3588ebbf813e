import { Server as SocketIOServer, Socket } from 'socket.io'
import { Server as HTTPServer } from 'http'
import { db } from '@/server/db'
import { flowRedisManager, ParticipantInfo } from '@/lib/redis'
import debounce from 'lodash.debounce'

export interface FlowChangeEvent {
  type: 'nodes_change' | 'edges_change' | 'viewport_change' | 'cursor_move'
  roomId: string
  userId: string
  data: any
  timestamp: number
}

export interface AuthenticatedSocket extends Socket {
  userId?: string
  roomId?: string
}

export class FlowWebSocketManager {
  private io: SocketIOServer
  private debouncedDatabaseSync: Map<string, ReturnType<typeof debounce>> = new Map()

  // New queue-based debouncing system
  private broadcastQueues: Map<string, FlowChangeEvent[]> = new Map()
  private broadcastTimers: Map<string, NodeJS.Timeout> = new Map()
  private databaseQueues: Map<string, FlowChangeEvent[]> = new Map()
  private databaseTimers: Map<string, NodeJS.Timeout> = new Map()

  constructor(server: HTTPServer) {
    this.io = new SocketIOServer(server, {
      cors: {
        origin: process.env.NEXTAUTH_URL || 'http://localhost:3000',
        methods: ['GET', 'POST']
      }
    })

    this.setupSocketHandlers()
    this.setupPeriodicSync()
  }

  private setupSocketHandlers() {
    this.io.on('connection', (socket: AuthenticatedSocket) => {
      console.log('Client connected:', socket.id)

      // Handle room joining
      socket.on('join_room', async (data: { roomId: string; token: string }) => {
        try {
          const { roomId, token } = data

          // Verify user authentication using the token
          const user = await this.authenticateUser(token)
          if (!user) {
            socket.emit('error', { message: 'Authentication failed' })
            return
          }

          // Check if user has access to the room
          const hasAccess = await this.checkRoomAccess(roomId, user.id)
          if (!hasAccess) {
            socket.emit('error', { message: 'Access denied to room' })
            return
          }

          // Leave previous room if any
          if (socket.roomId) {
            await this.leaveRoom(socket, socket.roomId)
          }

          // Join the new room
          socket.userId = user.id
          socket.roomId = roomId
          socket.join(roomId)

          // Add participant to Redis
          const participant: ParticipantInfo = {
            userId: user.id,
            name: user.name || 'Anonymous',
            role: await this.getUserRoleInRoom(roomId, user.id),
            lastActiveAt: new Date().toISOString()
          }

          await flowRedisManager.addParticipant(roomId, participant)

          // Get current room state with database fallback
          const roomData = await this.getRoomDataWithFallback(roomId)
          const participants = await flowRedisManager.getParticipants(roomId)

          // Send current state to the joining user
          socket.emit('room_joined', {
            roomId,
            flowData: roomData?.flowData,
            participants: participants.filter(p => p.userId !== user.id)
          })

          // Notify other participants
          socket.to(roomId).emit('participant_joined', participant)

          console.log(`User ${user.id} joined room ${roomId} successfully`)

        } catch (error) {
          console.error('Error joining room:', error)
          socket.emit('error', {
            message: 'Failed to join room',
            details: error instanceof Error ? error.message : 'Unknown error'
          })
        }
      })

      // Handle flow changes with queue-based debouncing
      socket.on('flow_change', async (event: Omit<FlowChangeEvent, 'timestamp'>) => {
        if (!socket.userId || !socket.roomId) return

        const flowEvent: FlowChangeEvent = {
          ...event,
          userId: socket.userId,
          roomId: socket.roomId,
          timestamp: Date.now()
        }

        try {
          // Store change in Redis immediately for persistence
          await flowRedisManager.addPendingChange(socket.roomId, flowEvent)

          // Queue for delayed broadcast (500ms)
          this.queueBroadcastEvent(socket.roomId, flowEvent)

          // Queue for delayed database sync (30 seconds)
          this.queueDatabaseSync(socket.roomId, flowEvent)

        } catch (error) {
          console.error('Error handling flow change:', error)
        }
      })

      // Handle cursor movement (high frequency, Redis only)
      socket.on('cursor_move', async (data: { x: number; y: number }) => {
        if (!socket.userId || !socket.roomId) return

        try {
          await flowRedisManager.updateParticipantCursor(
            socket.roomId,
            socket.userId,
            data
          )

          // Broadcast cursor position to other participants
          socket.to(socket.roomId).emit('cursor_move', {
            userId: socket.userId,
            cursor: data
          })
        } catch (error) {
          console.error('Error updating cursor:', error)
        }
      })

      // Handle disconnection
      socket.on('disconnect', async () => {
        console.log('Client disconnected:', socket.id)

        if (socket.userId && socket.roomId) {
          await this.leaveRoom(socket, socket.roomId)
        }
      })
    })
  }

  private async leaveRoom(socket: AuthenticatedSocket, roomId: string) {
    if (!socket.userId) return

    try {
      // Remove from Redis
      await flowRedisManager.removeParticipant(roomId, socket.userId)

      // Leave socket room
      socket.leave(roomId)

      // Notify other participants
      socket.to(roomId).emit('participant_left', { userId: socket.userId })

      // Clean up queues for this room if no participants left
      const room = this.io.sockets.adapter.rooms.get(roomId)
      if (!room || room.size === 0) {
        this.cleanupRoomQueues(roomId)
      }

    } catch (error) {
      console.error('Error leaving room:', error)
    }
  }

  private queueBroadcastEvent(roomId: string, event: FlowChangeEvent) {
    // Add event to broadcast queue
    if (!this.broadcastQueues.has(roomId)) {
      this.broadcastQueues.set(roomId, [])
    }
    this.broadcastQueues.get(roomId)!.push(event)

    // If no timer exists, create one (500ms delay)
    if (!this.broadcastTimers.has(roomId)) {
      const timer = setTimeout(() => {
        this.processBroadcastQueue(roomId)
      }, 500)

      this.broadcastTimers.set(roomId, timer)
      console.log(`Started broadcast timer for room ${roomId} (500ms delay)`)
    }
  }

  private async processBroadcastQueue(roomId: string) {
    try {
      const events = this.broadcastQueues.get(roomId) || []

      if (events.length === 0) return

      console.log(`Processing ${events.length} broadcast events for room ${roomId}`)

      // Get the latest state by applying all events in order
      const sortedEvents = events.sort((a, b) => a.timestamp - b.timestamp)

      // For efficiency, we only broadcast the final state
      const latestEvent = sortedEvents[sortedEvents.length - 1]

      // Broadcast the final state to all participants in the room
      this.io.to(roomId).emit('flow_change', latestEvent)

      // Clear the queue and timer
      this.broadcastQueues.delete(roomId)
      this.broadcastTimers.delete(roomId)

      console.log(`Broadcasted final state to room ${roomId}`)

    } catch (error) {
      console.error('Error processing broadcast queue:', error)
      // Clean up even on error
      this.broadcastQueues.delete(roomId)
      this.broadcastTimers.delete(roomId)
    }
  }

  private queueDatabaseSync(roomId: string, event: FlowChangeEvent) {
    // Add event to database queue
    if (!this.databaseQueues.has(roomId)) {
      this.databaseQueues.set(roomId, [])
    }
    this.databaseQueues.get(roomId)!.push(event)

    // If no timer exists, create one (30 seconds delay)
    if (!this.databaseTimers.has(roomId)) {
      const timer = setTimeout(() => {
        this.processDatabaseQueue(roomId)
      }, 30000) // 30 seconds

      this.databaseTimers.set(roomId, timer)
      console.log(`Started database sync timer for room ${roomId} (30s delay)`)
    }
  }

  private async processDatabaseQueue(roomId: string) {
    try {
      const events = this.databaseQueues.get(roomId) || []

      if (events.length === 0) return

      console.log(`Processing ${events.length} database sync events for room ${roomId}`)

      // Get current room data with database fallback
      const roomData = await this.getRoomDataWithFallback(roomId)
      if (!roomData) {
        console.warn(`Cannot sync room ${roomId} - room data not found`)
        return
      }

      // Apply all queued changes to flow data
      const updatedFlowData = this.applyChangesToFlowData(roomData.flowData, events)

      // Update database
      await db.flowRoom.update({
        where: { id: roomId },
        data: {
          flowData: updatedFlowData,
          updatedAt: new Date()
        }
      })

      // Update Redis cache
      await flowRedisManager.cacheFlowRoom(roomId, {
        ...roomData,
        flowData: updatedFlowData,
        lastSyncedAt: new Date().toISOString()
      })

      // Clear the queue and timer
      this.databaseQueues.delete(roomId)
      this.databaseTimers.delete(roomId)

      console.log(`Synced ${events.length} changes to database for room ${roomId}`)

    } catch (error) {
      console.error('Error processing database queue:', error)
      // Clean up even on error
      this.databaseQueues.delete(roomId)
      this.databaseTimers.delete(roomId)
    }
  }

  private cleanupRoomQueues(roomId: string) {
    // Clear broadcast queue and timer
    if (this.broadcastTimers.has(roomId)) {
      clearTimeout(this.broadcastTimers.get(roomId)!)
      this.broadcastTimers.delete(roomId)
    }
    this.broadcastQueues.delete(roomId)

    // Clear database queue and timer
    if (this.databaseTimers.has(roomId)) {
      clearTimeout(this.databaseTimers.get(roomId)!)
      this.databaseTimers.delete(roomId)
    }
    this.databaseQueues.delete(roomId)

    console.log(`Cleaned up queues for room ${roomId}`)
  }

  // Legacy method - keeping for backward compatibility with periodic sync
  private scheduleDatabaseSync(roomId: string) {
    // For periodic sync, we'll use the queue system too
    this.queueDatabaseSync(roomId, {
      type: 'periodic_sync',
      roomId,
      userId: 'system',
      data: {},
      timestamp: Date.now()
    } as FlowChangeEvent)
  }



  private applyChangesToFlowData(currentData: any, changes: FlowChangeEvent[]): any {
    // Sort changes by timestamp to apply them in order
    const sortedChanges = changes.sort((a, b) => a.timestamp - b.timestamp)

    const flowData = { ...currentData }

    for (const change of sortedChanges) {
      switch (change.type) {
        case 'nodes_change':
          flowData.nodes = change.data.nodes
          break
        case 'edges_change':
          flowData.edges = change.data.edges
          break
        case 'viewport_change':
          flowData.viewport = change.data.viewport
          break
      }
    }

    return flowData
  }

  private setupPeriodicSync() {
    // Force sync all active rooms to database every 5 minutes (backup mechanism)
    setInterval(async () => {
      try {
        const rooms = this.io.sockets.adapter.rooms

        for (const [roomId, room] of rooms) {
          // Skip user-specific rooms (socket IDs)
          if (room.size > 0 && !roomId.includes('socket_')) {
            // Force process any pending database queues
            if (this.databaseQueues.has(roomId) && this.databaseQueues.get(roomId)!.length > 0) {
              console.log(`Force processing database queue for room ${roomId} (periodic sync)`)
              await this.processDatabaseQueue(roomId)
            }
          }
        }
      } catch (error) {
        console.error('Error in periodic sync:', error)
      }
    }, 300000) // 5 minutes (backup mechanism)
  }

  private async authenticateUser(token: string) {
    // This would typically verify a JWT token or session
    // For now, we'll implement a basic approach
    try {
      // You might want to implement proper JWT verification here
      // For this example, we'll use a simple approach
      return await db.user.findFirst({
        where: { id: token } // In real implementation, decode JWT to get user ID
      })
    } catch {
      return null
    }
  }

  private async checkRoomAccess(roomId: string, userId: string): Promise<boolean> {
    try {
      const room = await db.flowRoom.findFirst({
        where: {
          id: roomId,
          OR: [
            { ownerId: userId },
            { isPublic: true },
            {
              participants: {
                some: { userId }
              }
            }
          ]
        }
      })

      return !!room
    } catch {
      return false
    }
  }

  private async getRoomDataWithFallback(roomId: string): Promise<any> {
    try {
      // First, try to get from Redis cache
      let roomData = await flowRedisManager.getFlowRoom(roomId)

      if (!roomData) {
        console.log(`Cache miss for room ${roomId}, loading from database`)

        // Cache miss - load from database
        const dbRoom = await db.flowRoom.findUnique({
          where: { id: roomId },
          select: {
            id: true,
            ownerId: true,
            flowData: true,
            updatedAt: true
          }
        })

        if (dbRoom) {
          // Create room data structure
          roomData = {
            roomId: dbRoom.id,
            ownerId: dbRoom.ownerId,
            flowData: dbRoom.flowData || { nodes: [], edges: [] },
            lastSyncedAt: dbRoom.updatedAt.toISOString()
          }

          // Cache it for future requests
          await flowRedisManager.cacheFlowRoom(roomId, roomData)
          console.log(`Cached room data for ${roomId} from database`)
        } else {
          console.warn(`Room ${roomId} not found in database`)
          return null
        }
      }

      return roomData
    } catch (error) {
      console.error('Error getting room data with fallback:', error)
      return null
    }
  }

  private async getUserRoleInRoom(roomId: string, userId: string): Promise<string> {
    try {
      const room = await db.flowRoom.findUnique({
        where: { id: roomId },
        include: {
          participants: {
            where: { userId }
          }
        }
      })

      if (room?.ownerId === userId) return 'OWNER'
      if (room?.participants[0]) return room.participants[0].role
      return 'VIEWER'
    } catch {
      return 'VIEWER'
    }
  }

  public getIO(): SocketIOServer {
    return this.io
  }

  // Debug methods for monitoring queue system
  public getQueueStatus(roomId?: string) {
    if (roomId) {
      return {
        roomId,
        broadcast: {
          queueLength: this.broadcastQueues.get(roomId)?.length || 0,
          hasTimer: this.broadcastTimers.has(roomId),
          events: this.broadcastQueues.get(roomId) || []
        },
        database: {
          queueLength: this.databaseQueues.get(roomId)?.length || 0,
          hasTimer: this.databaseTimers.has(roomId),
          events: this.databaseQueues.get(roomId) || []
        }
      }
    }

    // Return status for all rooms
    const allRooms = new Set([
      ...this.broadcastQueues.keys(),
      ...this.databaseQueues.keys()
    ])

    const status: any = {}
    for (const room of allRooms) {
      status[room] = {
        broadcast: {
          queueLength: this.broadcastQueues.get(room)?.length || 0,
          hasTimer: this.broadcastTimers.has(room)
        },
        database: {
          queueLength: this.databaseQueues.get(room)?.length || 0,
          hasTimer: this.databaseTimers.has(room)
        }
      }
    }

    return status
  }

  // Force process queues (for testing/debugging)
  public async forceProcessQueues(roomId: string) {
    const results = {
      broadcast: false,
      database: false
    }

    if (this.broadcastQueues.has(roomId)) {
      await this.processBroadcastQueue(roomId)
      results.broadcast = true
    }

    if (this.databaseQueues.has(roomId)) {
      await this.processDatabaseQueue(roomId)
      results.database = true
    }

    return results
  }
}

export let flowWebSocketManager: FlowWebSocketManager | null = null

export function initializeWebSocketManager(server: HTTPServer) {
  if (!flowWebSocketManager) {
    flowWebSocketManager = new FlowWebSocketManager(server)
  }
  return flowWebSocketManager
}
