'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { RefreshCw, Play, Clock, Database, Radio } from 'lucide-react'

interface QueueStatus {
  [roomId: string]: {
    broadcast: {
      queueLength: number
      hasTimer: boolean
    }
    database: {
      queueLength: number
      hasTimer: boolean
    }
  }
}

interface QueueMonitorProps {
  roomId?: string
  autoRefresh?: boolean
  refreshInterval?: number
}

export function QueueMonitor({ 
  roomId, 
  autoRefresh = true, 
  refreshInterval = 2000 
}: QueueMonitorProps) {
  const [queueStatus, setQueueStatus] = useState<QueueStatus>({})
  const [activeRooms, setActiveRooms] = useState<any>({})
  const [loading, setLoading] = useState(false)
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null)

  const fetchQueueStatus = async () => {
    try {
      setLoading(true)
      const url = roomId 
        ? `/api/debug/queue-status?roomId=${roomId}`
        : '/api/debug/queue-status'
      
      const response = await fetch(url)
      const data = await response.json()

      if (data.success) {
        setQueueStatus(data.data.queueStatus)
        setActiveRooms(data.data.activeRooms)
        setLastUpdate(new Date())
      }
    } catch (error) {
      console.error('Failed to fetch queue status:', error)
    } finally {
      setLoading(false)
    }
  }

  const forceProcessQueue = async (targetRoomId: string) => {
    try {
      const response = await fetch('/api/debug/queue-status', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          roomId: targetRoomId,
          action: 'force-process'
        })
      })

      const data = await response.json()
      if (data.success) {
        // Refresh status after force processing
        await fetchQueueStatus()
      }
    } catch (error) {
      console.error('Failed to force process queue:', error)
    }
  }

  useEffect(() => {
    fetchQueueStatus()

    if (autoRefresh) {
      const interval = setInterval(fetchQueueStatus, refreshInterval)
      return () => clearInterval(interval)
    }
  }, [roomId, autoRefresh, refreshInterval])

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString()
  }

  const getRoomIds = () => {
    const allRoomIds = new Set([
      ...Object.keys(queueStatus),
      ...Object.keys(activeRooms)
    ])
    return Array.from(allRoomIds)
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <h3 className="text-lg font-semibold">Queue Monitor</h3>
          {roomId && <Badge variant="outline">Room: {roomId}</Badge>}
        </div>
        
        <div className="flex items-center gap-2">
          {lastUpdate && (
            <span className="text-sm text-muted-foreground">
              Last update: {formatTime(lastUpdate)}
            </span>
          )}
          <Button
            variant="outline"
            size="sm"
            onClick={fetchQueueStatus}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          </Button>
        </div>
      </div>

      {/* Queue Status Cards */}
      <div className="grid gap-4">
        {getRoomIds().map(roomId => {
          const queue = queueStatus[roomId]
          const room = activeRooms[roomId]
          
          return (
            <Card key={roomId}>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-base">Room: {roomId}</CardTitle>
                  <div className="flex items-center gap-2">
                    {room && (
                      <Badge variant="secondary">
                        {room.participantCount} participant(s)
                      </Badge>
                    )}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => forceProcessQueue(roomId)}
                      disabled={!queue || (queue.broadcast.queueLength === 0 && queue.database.queueLength === 0)}
                    >
                      <Play className="h-3 w-3 mr-1" />
                      Force Process
                    </Button>
                  </div>
                </div>
              </CardHeader>
              
              <CardContent className="space-y-3">
                {queue ? (
                  <>
                    {/* Broadcast Queue */}
                    <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                      <div className="flex items-center gap-2">
                        <Radio className="h-4 w-4 text-blue-600" />
                        <span className="font-medium">Broadcast Queue</span>
                        <Badge variant={queue.broadcast.queueLength > 0 ? "default" : "secondary"}>
                          {queue.broadcast.queueLength} events
                        </Badge>
                      </div>
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4 text-blue-600" />
                        <span className="text-sm">500ms delay</span>
                        {queue.broadcast.hasTimer && (
                          <Badge variant="destructive">Timer Active</Badge>
                        )}
                      </div>
                    </div>

                    {/* Database Queue */}
                    <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                      <div className="flex items-center gap-2">
                        <Database className="h-4 w-4 text-green-600" />
                        <span className="font-medium">Database Queue</span>
                        <Badge variant={queue.database.queueLength > 0 ? "default" : "secondary"}>
                          {queue.database.queueLength} events
                        </Badge>
                      </div>
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4 text-green-600" />
                        <span className="text-sm">30s delay</span>
                        {queue.database.hasTimer && (
                          <Badge variant="destructive">Timer Active</Badge>
                        )}
                      </div>
                    </div>
                  </>
                ) : (
                  <div className="text-center py-4 text-muted-foreground">
                    No queue data available
                  </div>
                )}
              </CardContent>
            </Card>
          )
        })}
      </div>

      {getRoomIds().length === 0 && (
        <Card>
          <CardContent className="py-8 text-center text-muted-foreground">
            No active rooms or queues found
          </CardContent>
        </Card>
      )}

      {/* Legend */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Legend</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 text-sm">
          <div className="flex items-center gap-2">
            <Badge variant="destructive">Timer Active</Badge>
            <span>Queue is waiting for timer to fire</span>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="default">N events</Badge>
            <span>Number of events in queue</span>
          </div>
          <div className="text-muted-foreground">
            • Broadcast: Events are batched and sent to clients after 500ms
            <br />
            • Database: Events are batched and synced to database after 30 seconds
            <br />
            • New events add to queue without resetting timers
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
