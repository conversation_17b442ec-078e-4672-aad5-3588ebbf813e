import { NextResponse } from 'next/server'
import { redis } from '@/lib/redis'

export async function GET() {
  try {
    // Test Redis connection with a simple ping
    const result = await redis.ping()
    
    if (result === 'PONG') {
      // Test basic operations
      const testKey = `test:${Date.now()}`
      await redis.set(testKey, 'test-value', 'EX', 10) // Expire in 10 seconds
      const value = await redis.get(testKey)
      await redis.del(testKey)
      
      if (value === 'test-value') {
        return NextResponse.json({
          success: true,
          message: 'Redis is working correctly',
          details: {
            ping: result,
            setGet: 'OK',
            timestamp: new Date().toISOString(),
          }
        })
      } else {
        return NextResponse.json({
          success: false,
          error: 'Redis set/get operation failed',
        }, { status: 500 })
      }
    } else {
      return NextResponse.json({
        success: false,
        error: 'Redis ping failed',
      }, { status: 500 })
    }
  } catch (error) {
    console.error('Redis test error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown Redis error',
    }, { status: 500 })
  }
}
