import { NextResponse } from 'next/server'
import { db } from '@/server/db'
import { flowRedisManager } from '@/lib/redis'

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url)
  const roomId = searchParams.get('roomId')

  if (!roomId) {
    return NextResponse.json({
      success: false,
      error: 'roomId parameter is required'
    }, { status: 400 })
  }

  try {
    // Get room data to verify it exists
    const room = await db.flowRoom.findUnique({
      where: { id: roomId },
      select: {
        id: true,
        name: true,
        flowData: true,
        updatedAt: true
      }
    })

    if (!room) {
      return NextResponse.json({
        success: false,
        error: `Room ${roomId} not found`
      }, { status: 404 })
    }

    // Get pending changes from Redis
    const pendingChanges = await flowRedisManager.getAndClearPendingChanges(roomId)

    // Get cached room data
    const cachedData = await flowRedisManager.getFlowRoom(roomId)

    return NextResponse.json({
      success: true,
      message: 'Queue debouncing test data retrieved',
      data: {
        roomId: roomId,
        roomName: room.name,
        databaseState: {
          lastUpdated: room.updatedAt,
          nodeCount: room.flowData?.nodes?.length || 0,
          edgeCount: room.flowData?.edges?.length || 0
        },
        redisState: {
          isCached: !!cachedData,
          lastSynced: cachedData?.lastSyncedAt,
          pendingChangesCount: pendingChanges.length
        },
        queueInfo: {
          broadcastDelay: '500ms',
          databaseSyncDelay: '30 seconds',
          strategy: 'Queue accumulation (no timer reset)',
          description: 'Events accumulate in queues without resetting timers'
        },
        testInstructions: [
          '1. Open the collaborative flow page for this room',
          '2. Make rapid changes (drag nodes, add/remove elements)',
          '3. Observe that broadcasts are delayed by 500ms',
          '4. Database sync happens after 30 seconds of first change',
          '5. Multiple rapid changes accumulate without resetting timers'
        ]
      }
    })

  } catch (error) {
    console.error('Queue debouncing test error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json()
    const { 
      roomName = 'Queue Debouncing Test Room',
      testScenario = 'basic'
    } = body

    // Create a test room with initial data
    const initialFlowData = {
      nodes: [
        {
          id: 'test-node-1',
          type: 'default',
          position: { x: 100, y: 100 },
          data: { label: 'Test Node 1' }
        },
        {
          id: 'test-node-2',
          type: 'default',
          position: { x: 300, y: 100 },
          data: { label: 'Test Node 2' }
        }
      ],
      edges: [
        {
          id: 'test-edge-1',
          source: 'test-node-1',
          target: 'test-node-2',
          type: 'default'
        }
      ]
    }

    const testRoom = await db.flowRoom.create({
      data: {
        name: roomName,
        description: `Test room for queue-based debouncing (${testScenario})`,
        isPublic: false,
        ownerId: 'test-user-id', // You might want to use a real user ID
        flowData: initialFlowData
      }
    })

    // Cache the room data in Redis
    await flowRedisManager.cacheFlowRoom(testRoom.id, {
      roomId: testRoom.id,
      ownerId: testRoom.ownerId,
      flowData: testRoom.flowData,
      lastSyncedAt: new Date().toISOString()
    })

    return NextResponse.json({
      success: true,
      message: 'Queue debouncing test room created successfully',
      room: {
        id: testRoom.id,
        name: testRoom.name,
        ownerId: testRoom.ownerId,
        initialNodeCount: initialFlowData.nodes.length,
        initialEdgeCount: initialFlowData.edges.length
      },
      testUrls: {
        statusCheck: `/api/test/queue-debouncing?roomId=${testRoom.id}`,
        collaborativePage: `/reactflow/collaborative-page?roomId=${testRoom.id}`,
        directRoomAccess: `/flow/${testRoom.id}`
      },
      testingGuide: {
        broadcastTesting: {
          description: 'Test 500ms broadcast delay',
          steps: [
            'Open room in multiple browser tabs',
            'Make rapid changes in one tab',
            'Observe 500ms delay before other tabs see changes',
            'Verify multiple rapid changes are batched together'
          ]
        },
        databaseTesting: {
          description: 'Test 30-second database sync',
          steps: [
            'Make changes and wait 30 seconds',
            'Check database via status endpoint',
            'Verify changes are persisted',
            'Make more changes before 30s - should not reset timer'
          ]
        },
        queueAccumulation: {
          description: 'Test queue accumulation behavior',
          steps: [
            'Make change at T=0 (starts 30s timer)',
            'Make change at T=10s (adds to queue, no timer reset)',
            'Make change at T=20s (adds to queue, no timer reset)',
            'At T=30s, all 3 changes sync to database together'
          ]
        }
      }
    })

  } catch (error) {
    console.error('Error creating queue debouncing test room:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create test room'
    }, { status: 500 })
  }
}
