import { NextResponse } from 'next/server'
import { db } from '@/server/db'

export async function GET() {
  try {
    // Test database connection with a simple query
    const result = await db.$queryRaw`SELECT 1 as test`
    
    if (result) {
      // Test flow room table access
      const roomCount = await db.flowRoom.count()
      
      return NextResponse.json({
        success: true,
        message: 'Database is working correctly',
        details: {
          connection: 'OK',
          flowRoomCount: roomCount,
          timestamp: new Date().toISOString(),
        }
      })
    } else {
      return NextResponse.json({
        success: false,
        error: 'Database query failed',
      }, { status: 500 })
    }
  } catch (error) {
    console.error('Database test error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown database error',
    }, { status: 500 })
  }
}
