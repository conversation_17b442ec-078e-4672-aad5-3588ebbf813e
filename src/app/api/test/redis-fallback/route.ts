import { NextResponse } from 'next/server'
import { db } from '@/server/db'
import { flowRedisManager } from '@/lib/redis'

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url)
  const roomId = searchParams.get('roomId')

  if (!roomId) {
    return NextResponse.json({
      success: false,
      error: 'roomId parameter is required'
    }, { status: 400 })
  }

  try {
    console.log(`Testing Redis fallback for room: ${roomId}`)

    // Step 1: Check if room exists in database
    const dbRoom = await db.flowRoom.findUnique({
      where: { id: roomId },
      select: {
        id: true,
        name: true,
        ownerId: true,
        flowData: true,
        updatedAt: true
      }
    })

    if (!dbRoom) {
      return NextResponse.json({
        success: false,
        error: `Room ${roomId} not found in database`
      }, { status: 404 })
    }

    // Step 2: Clear Redis cache to simulate cache miss
    await flowRedisManager.cleanupRoom(roomId)
    console.log(`Cleared Redis cache for room ${roomId}`)

    // Step 3: Try to get room data (should trigger database fallback)
    const cachedData = await flowRedisManager.getFlowRoom(roomId)
    console.log(`Redis cache after cleanup: ${cachedData ? 'HIT' : 'MISS'}`)

    // Step 4: Simulate the fallback logic
    let roomData = await flowRedisManager.getFlowRoom(roomId)
    
    if (!roomData) {
      console.log('Cache miss detected, loading from database...')
      
      // This is the fallback logic
      roomData = {
        roomId: dbRoom.id,
        ownerId: dbRoom.ownerId,
        flowData: dbRoom.flowData || { nodes: [], edges: [] },
        lastSyncedAt: dbRoom.updatedAt.toISOString()
      }

      // Cache it for future requests
      await flowRedisManager.cacheFlowRoom(roomId, roomData)
      console.log('Room data cached successfully')
    }

    // Step 5: Verify the cache now contains the data
    const verifyCache = await flowRedisManager.getFlowRoom(roomId)

    return NextResponse.json({
      success: true,
      message: 'Redis fallback test completed successfully',
      details: {
        roomId: roomId,
        databaseRoom: {
          id: dbRoom.id,
          name: dbRoom.name,
          ownerId: dbRoom.ownerId,
          hasFlowData: !!dbRoom.flowData,
          updatedAt: dbRoom.updatedAt
        },
        fallbackData: {
          roomId: roomData.roomId,
          ownerId: roomData.ownerId,
          hasFlowData: !!roomData.flowData,
          lastSyncedAt: roomData.lastSyncedAt
        },
        cacheVerification: {
          isNowCached: !!verifyCache,
          cacheMatchesData: verifyCache?.roomId === roomData.roomId
        },
        testSteps: [
          '✅ Found room in database',
          '✅ Cleared Redis cache',
          '✅ Detected cache miss',
          '✅ Loaded data from database',
          '✅ Cached data in Redis',
          '✅ Verified cache contains data'
        ]
      }
    })

  } catch (error) {
    console.error('Redis fallback test error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      details: {
        roomId,
        timestamp: new Date().toISOString()
      }
    }, { status: 500 })
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json()
    const { roomName = 'Test Room for Redis Fallback' } = body

    // Create a test room for fallback testing
    const testRoom = await db.flowRoom.create({
      data: {
        name: roomName,
        description: 'Test room created for Redis fallback testing',
        isPublic: false,
        ownerId: 'test-user-id', // You might want to use a real user ID
        flowData: {
          nodes: [
            {
              id: 'test-node-1',
              type: 'default',
              position: { x: 100, y: 100 },
              data: { label: 'Test Node 1' }
            }
          ],
          edges: []
        }
      }
    })

    return NextResponse.json({
      success: true,
      message: 'Test room created successfully',
      room: {
        id: testRoom.id,
        name: testRoom.name,
        ownerId: testRoom.ownerId
      },
      testUrl: `/api/test/redis-fallback?roomId=${testRoom.id}`
    })

  } catch (error) {
    console.error('Error creating test room:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create test room'
    }, { status: 500 })
  }
}
