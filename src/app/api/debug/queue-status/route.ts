import { NextResponse } from 'next/server'
import { flowWebSocketManager } from '@/lib/websocket'

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url)
  const roomId = searchParams.get('roomId')
  const action = searchParams.get('action')

  try {
    if (!flowWebSocketManager) {
      return NextResponse.json({
        success: false,
        error: 'WebSocket manager not initialized'
      }, { status: 503 })
    }

    // Handle force processing action
    if (action === 'force-process' && roomId) {
      const results = await flowWebSocketManager.forceProcessQueues(roomId)
      return NextResponse.json({
        success: true,
        message: `Force processed queues for room ${roomId}`,
        results
      })
    }

    // Get queue status
    const queueStatus = flowWebSocketManager.getQueueStatus(roomId || undefined)

    // Get active rooms from Socket.IO
    const io = flowWebSocketManager.getIO()
    const activeRooms: any = {}
    
    for (const [roomId, room] of io.sockets.adapter.rooms) {
      // Skip user-specific rooms (socket IDs)
      if (!roomId.includes('socket_')) {
        activeRooms[roomId] = {
          participantCount: room.size,
          participants: Array.from(room)
        }
      }
    }

    return NextResponse.json({
      success: true,
      timestamp: new Date().toISOString(),
      data: {
        queueStatus,
        activeRooms,
        systemInfo: {
          totalActiveRooms: Object.keys(activeRooms).length,
          totalBroadcastQueues: Object.keys(queueStatus).filter(roomId => 
            queueStatus[roomId]?.broadcast?.queueLength > 0
          ).length,
          totalDatabaseQueues: Object.keys(queueStatus).filter(roomId => 
            queueStatus[roomId]?.database?.queueLength > 0
          ).length
        }
      },
      actions: {
        forceProcess: roomId ? `/api/debug/queue-status?roomId=${roomId}&action=force-process` : null,
        specificRoom: !roomId ? `/api/debug/queue-status?roomId=ROOM_ID` : null
      }
    })

  } catch (error) {
    console.error('Queue status debug error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json()
    const { roomId, action } = body

    if (!flowWebSocketManager) {
      return NextResponse.json({
        success: false,
        error: 'WebSocket manager not initialized'
      }, { status: 503 })
    }

    switch (action) {
      case 'force-process':
        if (!roomId) {
          return NextResponse.json({
            success: false,
            error: 'roomId required for force-process action'
          }, { status: 400 })
        }

        const results = await flowWebSocketManager.forceProcessQueues(roomId)
        return NextResponse.json({
          success: true,
          message: `Force processed queues for room ${roomId}`,
          results,
          timestamp: new Date().toISOString()
        })

      case 'get-status':
        const queueStatus = flowWebSocketManager.getQueueStatus(roomId)
        return NextResponse.json({
          success: true,
          data: queueStatus,
          timestamp: new Date().toISOString()
        })

      default:
        return NextResponse.json({
          success: false,
          error: `Unknown action: ${action}`,
          availableActions: ['force-process', 'get-status']
        }, { status: 400 })
    }

  } catch (error) {
    console.error('Queue status debug POST error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
