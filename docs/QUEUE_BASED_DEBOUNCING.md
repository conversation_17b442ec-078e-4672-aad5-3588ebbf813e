# Queue-Based Debouncing Implementation

## Overview

The new debouncing system implements **queue accumulation** instead of timer reset, providing more predictable behavior for collaborative editing scenarios.

## Key Changes

### **Before (Timer Reset Strategy)**
```
Event 1 → Start 500ms timer
Event 2 → Cancel timer, start new 500ms timer  ❌
Event 3 → Cancel timer, start new 500ms timer  ❌
Result: Only Event 3 gets processed after 500ms
```

### **After (Queue Accumulation Strategy)**
```
Event 1 → Start 500ms timer, add to queue
Event 2 → Add to queue (no timer reset)        ✅
Event 3 → Add to queue (no timer reset)        ✅
Result: All events processed together after 500ms
```

## Implementation Details

### **Broadcast Queue (500ms delay)**

```typescript
private queueBroadcastEvent(roomId: string, event: FlowChangeEvent) {
  // Add event to queue
  if (!this.broadcastQueues.has(roomId)) {
    this.broadcastQueues.set(roomId, [])
  }
  this.broadcastQueues.get(roomId)!.push(event)

  // Only start timer if none exists
  if (!this.broadcastTimers.has(roomId)) {
    const timer = setTimeout(() => {
      this.processBroadcastQueue(roomId)
    }, 500)
    
    this.broadcastTimers.set(roomId, timer)
  }
}
```

### **Database Queue (30 second delay)**

```typescript
private queueDatabaseSync(roomId: string, event: FlowChangeEvent) {
  // Add event to queue
  if (!this.databaseQueues.has(roomId)) {
    this.databaseQueues.set(roomId, [])
  }
  this.databaseQueues.get(roomId)!.push(event)

  // Only start timer if none exists
  if (!this.databaseTimers.has(roomId)) {
    const timer = setTimeout(() => {
      this.processDatabaseQueue(roomId)
    }, 30000) // 30 seconds
    
    this.databaseTimers.set(roomId, timer)
  }
}
```

## Data Structures

### **Queue Management**
```typescript
class FlowWebSocketManager {
  // Broadcast queues (500ms delay)
  private broadcastQueues: Map<string, FlowChangeEvent[]> = new Map()
  private broadcastTimers: Map<string, NodeJS.Timeout> = new Map()
  
  // Database queues (30s delay)  
  private databaseQueues: Map<string, FlowChangeEvent[]> = new Map()
  private databaseTimers: Map<string, NodeJS.Timeout> = new Map()
}
```

### **Event Flow**
```
User Action → Client Store (100ms debounce) → WebSocket Event
                                                    ↓
                                            Add to Broadcast Queue
                                                    ↓
                                            Add to Database Queue
                                                    ↓
                                            Store in Redis (immediate)
```

## Processing Logic

### **Broadcast Processing**
```typescript
private async processBroadcastQueue(roomId: string) {
  const events = this.broadcastQueues.get(roomId) || []
  
  // Sort by timestamp
  const sortedEvents = events.sort((a, b) => a.timestamp - b.timestamp)
  
  // Broadcast only the latest state (optimization)
  const latestEvent = sortedEvents[sortedEvents.length - 1]
  this.io.to(roomId).emit('flow_change', latestEvent)
  
  // Clear queue and timer
  this.broadcastQueues.delete(roomId)
  this.broadcastTimers.delete(roomId)
}
```

### **Database Processing**
```typescript
private async processDatabaseQueue(roomId: string) {
  const events = this.databaseQueues.get(roomId) || []
  
  // Apply all events to get final state
  const roomData = await this.getRoomDataWithFallback(roomId)
  const updatedFlowData = this.applyChangesToFlowData(roomData.flowData, events)
  
  // Single database write with all changes
  await db.flowRoom.update({
    where: { id: roomId },
    data: { flowData: updatedFlowData, updatedAt: new Date() }
  })
  
  // Clear queue and timer
  this.databaseQueues.delete(roomId)
  this.databaseTimers.delete(roomId)
}
```

## Timing Scenarios

### **Scenario 1: Rapid Editing**
```
T=0s:    User drags node → Queue: [Event1], Timer: 500ms
T=0.1s:  User drags node → Queue: [Event1, Event2], Timer: continues
T=0.2s:  User drags node → Queue: [Event1, Event2, Event3], Timer: continues
T=0.5s:  Timer fires → Broadcast Event3 (latest state)
```

### **Scenario 2: Database Sync**
```
T=0s:    First change → Queue: [Event1], Timer: 30s
T=10s:   Second change → Queue: [Event1, Event2], Timer: continues  
T=20s:   Third change → Queue: [Event1, Event2, Event3], Timer: continues
T=30s:   Timer fires → Sync all 3 events to database
```

### **Scenario 3: Mixed Activity**
```
T=0s:    Change → Broadcast timer: 500ms, DB timer: 30s
T=0.5s:  Broadcast fires → Send to clients
T=10s:   Another change → Add to DB queue (timer continues)
T=30s:   DB sync fires → Persist all changes
```

## Benefits

### **1. Predictable Timing**
- Broadcast always happens 500ms after first event
- Database sync always happens 30s after first event
- No timer resets from subsequent events

### **2. Efficient Batching**
- Multiple rapid changes batched into single broadcast
- Multiple changes synced to database in single transaction
- Reduces network and database load

### **3. Better User Experience**
- Users see changes within 500ms maximum
- No indefinite delays from continuous editing
- Consistent response times

### **4. Resource Optimization**
- Fewer database writes
- Fewer broadcast messages
- Better memory usage with queue cleanup

## Error Handling

### **Queue Cleanup**
```typescript
private cleanupRoomQueues(roomId: string) {
  // Clear broadcast queue and timer
  if (this.broadcastTimers.has(roomId)) {
    clearTimeout(this.broadcastTimers.get(roomId)!)
    this.broadcastTimers.delete(roomId)
  }
  this.broadcastQueues.delete(roomId)

  // Clear database queue and timer  
  if (this.databaseTimers.has(roomId)) {
    clearTimeout(this.databaseTimers.get(roomId)!)
    this.databaseTimers.delete(roomId)
  }
  this.databaseQueues.delete(roomId)
}
```

### **Automatic Cleanup**
- Queues cleaned when last user leaves room
- Error handling prevents memory leaks
- Periodic sync as backup mechanism (5 minutes)

## Testing

### **API Endpoint**
`/api/test/queue-debouncing`

### **Test Scenarios**
1. **Rapid Changes**: Make multiple quick edits, verify 500ms delay
2. **Queue Accumulation**: Verify events accumulate without timer reset
3. **Database Sync**: Verify 30-second database persistence
4. **Mixed Timing**: Test broadcast and database queues independently

### **Monitoring**
```bash
# Watch logs for queue activity
tail -f logs/websocket.log | grep "queue\|timer\|broadcast\|sync"
```

This implementation provides **predictable, efficient debouncing** that improves both user experience and system performance in collaborative editing scenarios.
