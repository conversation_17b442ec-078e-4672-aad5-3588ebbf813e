# Real-time Collaboration Setup Guide

This guide will help you set up the real-time collaboration feature for React Flow.

## Prerequisites

1. **Redis Server**: You need a Redis server running for real-time state management
2. **PostgreSQL Database**: Supabase or local PostgreSQL for persistent storage
3. **WebSocket Support**: The application uses Socket.IO for real-time communication

## Installation Steps

### 1. Install Redis

#### Option A: Using Docker (Recommended)
```bash
# Run Redis in a Docker container
docker run -d --name redis-collaboration -p 6379:6379 redis:alpine

# Or using Docker Compose (add to your docker-compose.yml)
version: '3.8'
services:
  redis:
    image: redis:alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  redis_data:
```

#### Option B: Local Installation
```bash
# On macOS
brew install redis
brew services start redis

# On Ubuntu/Debian
sudo apt update
sudo apt install redis-server
sudo systemctl start redis-server

# On Windows
# Download and install from: https://github.com/microsoftarchive/redis/releases
```

### 2. Environment Variables

Copy `.env.example` to `.env` and configure:

```env
# Database (Supabase or local PostgreSQL)
DATABASE_URL="postgresql://postgres:password@localhost:5432/nextjs_boilerplate"
DIRECT_URL="postgresql://postgres:password@localhost:5432/nextjs_boilerplate"

# Redis configuration
REDIS_URL="redis://localhost:6379"
REDIS_HOST="localhost"
REDIS_PORT="6379"
REDIS_PASSWORD=""

# WebSocket URL
NEXT_PUBLIC_WS_URL="http://localhost:3000"

# Auth configuration
NEXTAUTH_SECRET="your-secret-key"
NEXTAUTH_URL="http://localhost:3000"
```

### 3. Database Migration

Run the Prisma migration to create the collaboration tables:

```bash
npm run db:push
# or
npm run db:migrate
```

### 4. Start the Application

```bash
npm run dev
```

## Testing the Collaboration

1. **Create a Flow Room**:
   - Navigate to `/flow`
   - Click "Create Room"
   - Fill in room details and create

2. **Test Real-time Features**:
   - Open the same room in multiple browser tabs/windows
   - Try dragging nodes - you should see changes in real-time
   - Move your cursor - other participants should see your cursor
   - Add/delete nodes and edges

3. **Check Connection Status**:
   - Look for the "Connected" indicator in the top-left
   - Check the collaboration panel on the right for active participants

## Architecture Overview

### Components

1. **WebSocket Server** (`src/lib/websocket.ts`):
   - Handles real-time connections
   - Manages room joining/leaving
   - Broadcasts changes to participants

2. **Redis Manager** (`src/lib/redis.ts`):
   - Caches room state for fast access
   - Stores pending changes for batch processing
   - Manages participant information

3. **Collaborative Store** (`src/stores/collaborative-flow-store.ts`):
   - Zustand store for client-side state
   - Handles WebSocket connections
   - Debounces changes to prevent spam

4. **Database Layer** (`src/server/api/routers/flow-room.ts`):
   - CRUD operations for flow rooms
   - Participant management
   - Persistent storage

### Data Flow

1. **User Action** → Client Store → Debounced Event → WebSocket
2. **WebSocket** → Redis Cache → Broadcast to Other Clients
3. **Periodic Sync** → Redis → PostgreSQL Database

### Debouncing Strategy

- **Node Dragging**: 100ms debounce to prevent excessive updates
- **Cursor Movement**: 50ms debounce for smooth tracking
- **Database Sync**: 2-second debounce for batch writes

## Troubleshooting

### Redis Connection Issues
```bash
# Test Redis connection
redis-cli ping
# Should return: PONG

# Check Redis logs
docker logs redis-collaboration
```

### WebSocket Connection Issues
- Check browser console for connection errors
- Verify `NEXT_PUBLIC_WS_URL` matches your development URL
- Ensure no firewall blocking WebSocket connections

### Database Issues
```bash
# Reset database
npm run db:push --force-reset

# Check database connection
npx prisma studio
```

### Performance Optimization

1. **Redis Memory**: Monitor Redis memory usage for large rooms
2. **WebSocket Connections**: Limit concurrent connections if needed
3. **Database Writes**: Adjust debounce timing based on usage patterns

## Production Deployment

### Redis Configuration
- Use Redis Cluster for high availability
- Configure Redis persistence (RDB + AOF)
- Set appropriate memory limits and eviction policies

### WebSocket Scaling
- Use Redis adapter for Socket.IO clustering
- Configure load balancer for sticky sessions
- Monitor connection counts and memory usage

### Database Optimization
- Index frequently queried fields
- Set up read replicas for heavy read workloads
- Configure connection pooling

## Security Considerations

1. **Authentication**: All WebSocket connections require valid session tokens
2. **Authorization**: Room access is controlled by ownership and participant roles
3. **Rate Limiting**: Consider implementing rate limits for WebSocket events
4. **Data Validation**: All incoming data is validated before processing

## Monitoring

Key metrics to monitor:
- WebSocket connection count
- Redis memory usage
- Database query performance
- Real-time event frequency
- User session duration

## Support

If you encounter issues:
1. Check the browser console for errors
2. Verify Redis and database connections
3. Review server logs for WebSocket errors
4. Test with a simple room setup first
