#!/usr/bin/env node

/**
 * Collaboration System Test Script
 * 
 * This script tests the real-time collaboration features by:
 * 1. Checking Redis connection
 * 2. Testing database connectivity
 * 3. Creating a test room
 * 4. Simulating WebSocket connections
 */

const { createClient } = require('redis');
const { PrismaClient } = require('@prisma/client');
const { io } = require('socket.io-client');

// Configuration
const REDIS_URL = process.env.REDIS_URL || 'redis://localhost:6379';
const DATABASE_URL = process.env.DATABASE_URL;
const WS_URL = process.env.NEXT_PUBLIC_WS_URL || 'http://localhost:3000';

// Test results
const results = {
  redis: { status: 'pending', message: '' },
  database: { status: 'pending', message: '' },
  websocket: { status: 'pending', message: '' },
  room: { status: 'pending', message: '' },
  realtime: { status: 'pending', message: '' }
};

// Utility functions
const log = (test, status, message) => {
  results[test] = { status, message };
  const icon = status === 'success' ? '✅' : status === 'error' ? '❌' : '⏳';
  console.log(`${icon} ${test.toUpperCase()}: ${message}`);
};

const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Test Redis connection
async function testRedis() {
  try {
    log('redis', 'pending', 'Connecting to Redis...');
    
    const client = createClient({ url: REDIS_URL });
    await client.connect();
    
    // Test basic operations
    const testKey = `test:${Date.now()}`;
    await client.set(testKey, 'test-value', { EX: 10 });
    const value = await client.get(testKey);
    await client.del(testKey);
    
    if (value === 'test-value') {
      log('redis', 'success', 'Redis connection and operations working');
    } else {
      log('redis', 'error', 'Redis set/get operation failed');
    }
    
    await client.disconnect();
  } catch (error) {
    log('redis', 'error', `Redis test failed: ${error.message}`);
  }
}

// Test database connection
async function testDatabase() {
  try {
    log('database', 'pending', 'Testing database connection...');
    
    if (!DATABASE_URL) {
      log('database', 'error', 'DATABASE_URL not configured');
      return;
    }
    
    const prisma = new PrismaClient();
    
    // Test basic query
    await prisma.$queryRaw`SELECT 1 as test`;
    
    // Test flow room table
    const roomCount = await prisma.flowRoom.count();
    
    log('database', 'success', `Database connected. Found ${roomCount} flow rooms.`);
    
    await prisma.$disconnect();
  } catch (error) {
    log('database', 'error', `Database test failed: ${error.message}`);
  }
}

// Test WebSocket connection
async function testWebSocket() {
  return new Promise((resolve) => {
    try {
      log('websocket', 'pending', 'Testing WebSocket connection...');
      
      const socket = io(WS_URL, {
        transports: ['websocket', 'polling'],
        timeout: 5000
      });
      
      const timeout = setTimeout(() => {
        log('websocket', 'error', 'WebSocket connection timeout');
        socket.disconnect();
        resolve();
      }, 5000);
      
      socket.on('connect', () => {
        clearTimeout(timeout);
        log('websocket', 'success', 'WebSocket connection established');
        socket.disconnect();
        resolve();
      });
      
      socket.on('connect_error', (error) => {
        clearTimeout(timeout);
        log('websocket', 'error', `WebSocket connection failed: ${error.message}`);
        resolve();
      });
      
    } catch (error) {
      log('websocket', 'error', `WebSocket test failed: ${error.message}`);
      resolve();
    }
  });
}

// Test room creation
async function testRoomCreation() {
  try {
    log('room', 'pending', 'Testing room creation...');
    
    const prisma = new PrismaClient();
    
    // Create a test user if needed
    let testUser = await prisma.user.findFirst({
      where: { email: '<EMAIL>' }
    });
    
    if (!testUser) {
      testUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Test User',
        }
      });
    }
    
    // Create a test room
    const testRoom = await prisma.flowRoom.create({
      data: {
        name: `Test Room ${Date.now()}`,
        description: 'Automated test room',
        ownerId: testUser.id,
        flowData: {
          nodes: [
            {
              id: '1',
              type: 'input',
              position: { x: 250, y: 25 },
              data: { label: 'Test Node' }
            }
          ],
          edges: []
        }
      }
    });
    
    log('room', 'success', `Test room created: ${testRoom.id.slice(0, 8)}...`);
    
    // Clean up
    await prisma.flowRoom.delete({ where: { id: testRoom.id } });
    
    await prisma.$disconnect();
  } catch (error) {
    log('room', 'error', `Room creation test failed: ${error.message}`);
  }
}

// Main test runner
async function runTests() {
  console.log('🚀 Starting Collaboration System Tests\n');
  
  await testRedis();
  await sleep(1000);
  
  await testDatabase();
  await sleep(1000);
  
  await testWebSocket();
  await sleep(1000);
  
  await testRoomCreation();
  
  // Summary
  console.log('\n📊 Test Summary:');
  console.log('================');
  
  let passed = 0;
  let failed = 0;
  
  Object.entries(results).forEach(([test, result]) => {
    const icon = result.status === 'success' ? '✅' : '❌';
    console.log(`${icon} ${test.toUpperCase()}: ${result.status}`);
    
    if (result.status === 'success') passed++;
    else failed++;
  });
  
  console.log(`\n✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  
  if (failed === 0) {
    console.log('\n🎉 All tests passed! Collaboration system is ready.');
  } else {
    console.log('\n⚠️  Some tests failed. Check the setup guide in COLLABORATION.md');
  }
  
  process.exit(failed > 0 ? 1 : 0);
}

// Run tests if called directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { runTests, testRedis, testDatabase, testWebSocket, testRoomCreation };
