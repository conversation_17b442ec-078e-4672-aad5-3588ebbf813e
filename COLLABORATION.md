# Real-time Collaboration for React Flow

This document describes the complete real-time collaboration system implemented for React Flow diagrams.

## 🚀 Features

- **Real-time synchronization** of nodes, edges, and cursor positions
- **Debounced updates** to prevent excessive network requests during drag operations
- **PostgreSQL persistence** with <PERSON><PERSON><PERSON> for reliable data storage
- **Redis caching** for fast real-time state management
- **WebSocket connections** using Socket.IO for instant updates
- **User presence indicators** showing who's currently active
- **Role-based permissions** (Owner, Editor, Viewer)
- **Automatic reconnection** handling for network interruptions

## 🏗️ Architecture

### Data Flow
```
User Action → Client Store → Debounced Event → WebSocket → Redis → Broadcast
                                                      ↓
                                              Periodic Database Sync
```

### Components Overview

1. **Client-side Store** (`src/stores/collaborative-flow-store.ts`)
   - Zustand store managing local state
   - WebSocket connection handling
   - Debounced change emission

2. **WebSocket Server** (`src/lib/websocket.ts`)
   - Socket.IO server for real-time communication
   - Room management and user authentication
   - Event broadcasting to participants

3. **Redis Manager** (`src/lib/redis.ts`)
   - Fast caching layer for room state
   - Participant tracking
   - Pending changes queue

4. **Database Layer** (`src/server/api/routers/flow-room.ts`)
   - CRUD operations for flow rooms
   - User permissions management
   - Persistent data storage

## 🛠️ Setup Instructions

### 1. Prerequisites

- **Node.js 18+**
- **PostgreSQL** (Supabase recommended)
- **Redis Server**

### 2. Install Redis

#### Using Docker (Recommended)
```bash
docker run -d --name redis-collaboration -p 6379:6379 redis:alpine
```

#### Using Package Manager
```bash
# macOS
brew install redis && brew services start redis

# Ubuntu/Debian
sudo apt install redis-server && sudo systemctl start redis-server
```

### 3. Environment Configuration

```env
# Database
DATABASE_URL="********************************/db"
DIRECT_URL="********************************/db"

# Redis
REDIS_URL="redis://localhost:6379"
REDIS_HOST="localhost"
REDIS_PORT="6379"
REDIS_PASSWORD=""

# WebSocket
NEXT_PUBLIC_WS_URL="http://localhost:3000"

# Authentication
NEXTAUTH_SECRET="your-secret-key"
NEXTAUTH_URL="http://localhost:3000"
```

### 4. Database Migration

```bash
npm run db:push
```

### 5. Start Development Server

```bash
npm run dev
```

## 📱 Usage

### Creating a Flow Room

1. Navigate to `/flow`
2. Click "Create Room"
3. Fill in room details:
   - **Name**: Room identifier
   - **Description**: Optional description
   - **Public**: Whether room is publicly accessible

### Joining a Room

1. Access room via `/flow/[roomId]`
2. Authentication required for private rooms
3. Real-time connection established automatically

### Collaboration Features

- **Node Editing**: Drag, add, delete nodes in real-time
- **Edge Management**: Create and delete connections
- **Cursor Tracking**: See other users' cursor positions
- **Presence Indicators**: View active participants
- **Permission Control**: Owner/Editor/Viewer roles

## 🔧 Technical Details

### Debouncing Strategy

| Event Type | Debounce Time | Reason |
|------------|---------------|---------|
| Node Dragging | 100ms | Smooth dragging without spam |
| Cursor Movement | 50ms | Responsive cursor tracking |
| Database Sync | 2000ms | Batch database writes |

### WebSocket Events

#### Client → Server
- `join_room`: Join a collaboration room
- `flow_change`: Node/edge modifications
- `cursor_move`: Cursor position updates

#### Server → Client
- `room_joined`: Successful room join with initial state
- `flow_change`: Broadcast changes from other users
- `participant_joined/left`: User presence updates
- `cursor_move`: Other users' cursor positions

### Redis Data Structure

```
flow:room:{roomId}              # Room state cache
flow:room:{roomId}:participants # Active participants
flow:room:{roomId}:changes      # Pending changes queue
```

### Database Schema

```sql
-- Flow rooms
CREATE TABLE FlowRoom (
  id          TEXT PRIMARY KEY,
  name        TEXT NOT NULL,
  description TEXT,
  ownerId     TEXT NOT NULL,
  flowData    JSON,
  isPublic    BOOLEAN DEFAULT false,
  createdAt   TIMESTAMP DEFAULT now(),
  updatedAt   TIMESTAMP DEFAULT now()
);

-- Room participants
CREATE TABLE FlowRoomParticipant (
  id       TEXT PRIMARY KEY,
  roomId   TEXT NOT NULL,
  userId   TEXT NOT NULL,
  role     TEXT DEFAULT 'VIEWER',
  joinedAt TIMESTAMP DEFAULT now(),
  UNIQUE(roomId, userId)
);
```

## 🧪 Testing

### Automated Tests

Visit `/flow/test` to run system tests:
- Redis connection
- Database connectivity
- WebSocket server
- Room creation
- Real-time synchronization

### Manual Testing

1. **Multi-tab Test**: Open same room in multiple tabs
2. **Network Interruption**: Disconnect/reconnect network
3. **Concurrent Editing**: Multiple users editing simultaneously
4. **Permission Test**: Test different user roles

## 🚀 Production Deployment

### Redis Configuration

```bash
# Redis cluster for high availability
redis-server --cluster-enabled yes --cluster-config-file nodes.conf
```

### Environment Variables

```env
# Production Redis (e.g., Redis Cloud)
REDIS_URL="rediss://user:pass@host:port"

# WebSocket URL (your domain)
NEXT_PUBLIC_WS_URL="https://yourdomain.com"

# Database (production Supabase)
DATABASE_URL="postgresql://postgres:<EMAIL>:5432/postgres"
```

### Scaling Considerations

1. **Redis Clustering**: For high availability
2. **WebSocket Load Balancing**: Sticky sessions required
3. **Database Connection Pooling**: Use PgBouncer
4. **CDN**: Static assets and API caching

## 🔒 Security

### Authentication
- All WebSocket connections require valid session tokens
- Room access controlled by ownership and participant roles

### Authorization
- **Owner**: Full control (edit, delete, manage participants)
- **Editor**: Can modify flow content
- **Viewer**: Read-only access

### Data Validation
- All incoming WebSocket data validated
- SQL injection prevention via Prisma
- XSS protection on user inputs

## 📊 Monitoring

### Key Metrics
- Active WebSocket connections
- Redis memory usage
- Database query performance
- Real-time event frequency
- User session duration

### Logging
- WebSocket connection/disconnection events
- Room join/leave activities
- Error tracking and debugging
- Performance metrics

## 🐛 Troubleshooting

### Common Issues

1. **Redis Connection Failed**
   ```bash
   # Check Redis status
   redis-cli ping
   # Should return: PONG
   ```

2. **WebSocket Not Connecting**
   - Verify `NEXT_PUBLIC_WS_URL` environment variable
   - Check browser console for connection errors
   - Ensure no firewall blocking WebSocket ports

3. **Database Sync Issues**
   ```bash
   # Reset database
   npm run db:push --force-reset
   ```

4. **Performance Issues**
   - Monitor Redis memory usage
   - Check database query performance
   - Adjust debounce timings if needed

### Debug Mode

Enable debug logging:
```env
DEBUG=socket.io:*
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Implement changes with tests
4. Submit a pull request

### Development Guidelines

- Follow TypeScript strict mode
- Add tests for new features
- Update documentation
- Use conventional commits

## 📄 License

This collaboration system is part of the Next.js boilerplate project and follows the same license terms.
